// Google Analytics utility functions
declare global {
  interface Window {
    gtag?: (command: string, targetId: string, config?: Record<string, any>) => void;
    dataLayer?: any[];
  }
}

export const analytics = {
  // Track text processing events
  trackTextProcessed: (options: {
    style: string;
    intensity: string;
    textLength: number;
    processingTime: number;
    hasVariations: boolean;
  }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'text_processed', {
        event_category: 'text_processing',
        event_label: options.style,
        custom_parameters: {
          style: options.style,
          intensity: options.intensity,
          text_length: options.textLength,
          processing_time: options.processingTime,
          has_variations: options.hasVariations
        }
      });
    }
  },

  // Track copy actions
  trackCopy: (source: 'main' | 'variation', variationIndex?: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'text_copied', {
        event_category: 'user_interaction',
        event_label: source,
        custom_parameters: {
          source,
          variation_index: variationIndex || 0
        }
      });
    }
  },

  // Track download actions
  trackDownload: (source: 'main' | 'variation', variationIndex?: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'text_downloaded', {
        event_category: 'user_interaction',
        event_label: source,
        custom_parameters: {
          source,
          variation_index: variationIndex || 0
        }
      });
    }
  },

  // Track file uploads
  trackFileUpload: (fileType: string, fileSize: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'file_uploaded', {
        event_category: 'file_interaction',
        event_label: fileType,
        custom_parameters: {
          file_type: fileType,
          file_size_kb: Math.round(fileSize / 1024)
        }
      });
    }
  },

  // Track style changes
  trackStyleChange: (fromStyle: string, toStyle: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'style_changed', {
        event_category: 'settings',
        event_label: `${fromStyle}_to_${toStyle}`,
        custom_parameters: {
          from_style: fromStyle,
          to_style: toStyle
        }
      });
    }
  },

  // Track intensity changes
  trackIntensityChange: (fromIntensity: string, toIntensity: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'intensity_changed', {
        event_category: 'settings',
        event_label: `${fromIntensity}_to_${toIntensity}`,
        custom_parameters: {
          from_intensity: fromIntensity,
          to_intensity: toIntensity
        }
      });
    }
  },

  // Track variations toggle
  trackVariationsToggle: (enabled: boolean) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'variations_toggled', {
        event_category: 'settings',
        event_label: enabled ? 'enabled' : 'disabled',
        custom_parameters: {
          enabled: enabled
        }
      });
    }
  },

  // Track tab switches
  trackTabSwitch: (tab: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'tab_switched', {
        event_category: 'navigation',
        event_label: tab,
        custom_parameters: {
          tab_name: tab
        }
      });
    }
  },

  // Track errors
  trackError: (errorType: string, errorMessage: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'error_occurred', {
        event_category: 'errors',
        event_label: errorType,
        custom_parameters: {
          error_type: errorType,
          error_message: errorMessage.substring(0, 100)
        }
      });
    }
  },

  // Track page views (automatic with Google Analytics)
  trackPageView: () => {
    // Page views are automatically tracked by Google Analytics
    // This function is here for completeness
  }
};

export default analytics;
