/**
 * Language Detection System for GhostLayer
 * Detects English, Vietnamese, and Chinese text with high accuracy
 */

import { SupportedLanguage, LanguageDetectionResult } from './types';

// Unicode ranges for different languages
const UNICODE_RANGES = {
  // Chinese characters (CJK Unified Ideographs)
  chinese: /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/,
  
  // Vietnamese diacritics and special characters
  vietnamese: /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ]/,
  
  // Latin characters (basic English and extended)
  latin: /[a-zA-Z\u00C0-\u017F]/,
  
  // Chinese punctuation
  chinesePunctuation: /[。！？；：，、""''（）【】《》]/,
  
  // Western punctuation
  westernPunctuation: /[.!?;:,'"()\[\]{}]/
};

// Language-specific character frequency patterns
const LANGUAGE_PATTERNS = {
  chinese: {
    // Common Chinese characters
    common: /[的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞]/,
    
    // Traditional Chinese indicators
    traditional: /[繁體中文台灣香港澳門]/,
    
    // Simplified Chinese indicators
    simplified: /[简体中文大陆]/
  },
  
  vietnamese: {
    // Common Vietnamese words
    common: /\b(và|của|trong|với|để|có|là|được|này|đó|những|các|một|hai|ba|bốn|năm|sáu|bảy|tám|chín|mười|người|thời|gian|việc|làm|nước|nhà|đất|trời|con|em|anh|chị|ông|bà|tôi|bạn|họ|chúng|mình|đây|đấy|khi|nào|sao|gì|ai|đâu|như|thế|rất|lắm|nhiều|ít|lớn|nhỏ|cao|thấp|dài|ngắn|rộng|hẹp|đẹp|xấu|tốt|xấu|mới|cũ|trẻ|già|nhanh|chậm|sớm|muộn|xa|gần|trên|dưới|trong|ngoài|trước|sau|bên|giữa|đầu|cuối)\b/gi,
    
    // Vietnamese tone markers
    tones: /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹ]/
  },
  
  english: {
    // Common English words
    common: /\b(the|be|to|of|and|a|in|that|have|i|it|for|not|on|with|he|as|you|do|at|this|but|his|by|from|they|she|or|an|will|my|one|all|would|there|their|what|so|up|out|if|about|who|get|which|go|me|when|make|can|like|time|no|just|him|know|take|people|into|year|your|good|some|could|them|see|other|than|then|now|look|only|come|its|over|think|also|back|after|use|two|how|our|work|first|well|way|even|new|want|because|any|these|give|day|most|us)\b/gi,
    
    // English-specific patterns
    articles: /\b(a|an|the)\b/gi,
    contractions: /\b\w+[''](?:t|re|ve|ll|d|s|m)\b/gi
  }
};

/**
 * Detects the language of input text
 */
export function detectLanguage(text: string): LanguageDetectionResult {
  if (!text || text.trim().length === 0) {
    return {
      language: 'en',
      confidence: 0,
      detectedFeatures: {
        hasChineseChars: false,
        hasVietnameseDiacritics: false,
        hasLatinChars: false,
        punctuationStyle: 'western'
      }
    };
  }

  const cleanText = text.trim();
  const textLength = cleanText.length;
  
  // Count character types
  const chineseMatches = (cleanText.match(UNICODE_RANGES.chinese) || []).length;
  const vietnameseMatches = (cleanText.match(UNICODE_RANGES.vietnamese) || []).length;
  const latinMatches = (cleanText.match(UNICODE_RANGES.latin) || []).length;
  
  // Calculate percentages
  const chinesePercentage = chineseMatches / textLength;
  const vietnamesePercentage = vietnameseMatches / textLength;
  const latinPercentage = latinMatches / textLength;
  
  // Detect punctuation style
  const chinesePuncCount = (cleanText.match(UNICODE_RANGES.chinesePunctuation) || []).length;
  const westernPuncCount = (cleanText.match(UNICODE_RANGES.westernPunctuation) || []).length;
  
  let punctuationStyle: 'western' | 'chinese' | 'mixed' = 'western';
  if (chinesePuncCount > westernPuncCount) {
    punctuationStyle = 'chinese';
  } else if (chinesePuncCount > 0 && westernPuncCount > 0) {
    punctuationStyle = 'mixed';
  }
  
  // Advanced pattern matching
  const chineseCommonMatches = (cleanText.match(LANGUAGE_PATTERNS.chinese.common) || []).length;
  const vietnameseCommonMatches = (cleanText.match(LANGUAGE_PATTERNS.vietnamese.common) || []).length;
  const englishCommonMatches = (cleanText.match(LANGUAGE_PATTERNS.english.common) || []).length;
  
  // Vietnamese tone marker detection
  const vietnameseToneMatches = (cleanText.match(LANGUAGE_PATTERNS.vietnamese.tones) || []).length;
  
  // Decision logic with confidence scoring
  let language: SupportedLanguage = 'en';
  let confidence = 0;
  
  // Chinese detection (highest priority for CJK characters)
  if (chinesePercentage > 0.1 || chineseCommonMatches > 2) {
    // Determine if Traditional or Simplified Chinese
    const traditionalMatches = (cleanText.match(LANGUAGE_PATTERNS.chinese.traditional) || []).length;
    const simplifiedMatches = (cleanText.match(LANGUAGE_PATTERNS.chinese.simplified) || []).length;
    
    language = traditionalMatches > simplifiedMatches ? 'zh-tw' : 'zh';
    confidence = Math.min(95, 60 + (chinesePercentage * 100) + (chineseCommonMatches * 5));
  }
  // Vietnamese detection
  else if (vietnamesePercentage > 0.02 || vietnameseToneMatches > 1 || vietnameseCommonMatches > 1) {
    language = 'vi';
    confidence = Math.min(95, 50 + (vietnamesePercentage * 200) + (vietnameseToneMatches * 10) + (vietnameseCommonMatches * 15));
  }
  // English detection (default with validation)
  else {
    language = 'en';
    const englishIndicators = englishCommonMatches + (latinPercentage > 0.7 ? 20 : 0);
    confidence = Math.min(95, Math.max(30, englishIndicators * 2));
  }
  
  // Boost confidence for clear indicators
  if (punctuationStyle === 'chinese' && (language === 'zh' || language === 'zh-tw')) {
    confidence = Math.min(98, confidence + 10);
  }
  
  return {
    language,
    confidence,
    detectedFeatures: {
      hasChineseChars: chineseMatches > 0,
      hasVietnameseDiacritics: vietnameseMatches > 0,
      hasLatinChars: latinMatches > 0,
      punctuationStyle
    }
  };
}

/**
 * Validates if text is suitable for processing in the detected language
 */
export function validateLanguageSupport(text: string, targetLanguage?: SupportedLanguage): { isSupported: boolean; detectedLanguage: SupportedLanguage; confidence: number; message?: string } {
  const detection = detectLanguage(text);
  const finalLanguage = targetLanguage || detection.language;
  
  // Check if we have sufficient confidence
  if (detection.confidence < 30) {
    return {
      isSupported: false,
      detectedLanguage: detection.language,
      confidence: detection.confidence,
      message: 'Unable to reliably detect language. Text may be too short or contain mixed languages.'
    };
  }
  
  // Check for language mismatch
  if (targetLanguage && targetLanguage !== detection.language && detection.confidence > 70) {
    return {
      isSupported: false,
      detectedLanguage: detection.language,
      confidence: detection.confidence,
      message: `Text appears to be in ${detection.language} but ${targetLanguage} processing was requested.`
    };
  }
  
  return {
    isSupported: true,
    detectedLanguage: finalLanguage,
    confidence: detection.confidence
  };
}
