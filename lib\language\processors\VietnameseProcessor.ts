/**
 * Vietnamese Language Processor for GhostLayer
 * Handles Vietnamese text humanization with diacritic-aware processing
 */

import { BaseProcessor } from './BaseProcessor';
import { SynonymDictionary } from '../types';

export class VietnameseProcessor extends BaseProcessor {
  constructor() {
    super('vi');
  }

  /**
   * Optimizes text for specific writing style
   */
  optimizeForStyle(text: string, style: string): string {
    let result = text;

    switch (style) {
      case 'formal':
        result = this.applyFormalStyle(result);
        break;
      case 'casual':
        result = this.applyCasualStyle(result);
        break;
      case 'academic':
        result = this.applyAcademicStyle(result);
        break;
      case 'creative':
        result = this.applyCreativeStyle(result);
        break;
      case 'technical':
        result = this.applyTechnicalStyle(result);
        break;
      case 'balanced':
      default:
        result = this.applyBalancedStyle(result);
        break;
    }

    return result;
  }

  /**
   * Loads Vietnamese synonym dictionary
   */
  protected async loadSynonyms(): Promise<SynonymDictionary> {
    // Vietnamese synonym dictionary with ~2,000 common word pairs
    return {
      // Common verbs (động từ)
      "là": ["được", "thành", "trở thành", "tồn tại"],
      "có": ["sở hữu", "tồn tại", "chứa đựng", "bao gồm"],
      "làm": ["thực hiện", "tiến hành", "tạo ra", "sản xuất", "hoàn thành"],
      "đi": ["di chuyển", "tiến tới", "hướng tới", "rời khỏi"],
      "đến": ["tới", "đạt tới", "tiếp cận", "xuất hiện"],
      "nói": ["phát biểu", "tuyên bố", "bày tỏ", "trình bày", "diễn đạt"],
      "biết": ["hiểu", "nhận thức", "am hiểu", "nắm bắt", "thông hiểu"],
      "thấy": ["nhìn thấy", "quan sát", "phát hiện", "nhận ra", "chứng kiến"],
      "muốn": ["mong muốn", "khao khát", "ước ao", "mong ước", "ham muốn"],
      "cần": ["cần thiết", "đòi hỏi", "yêu cầu", "thiết yếu"],
      "được": ["có thể", "được phép", "đạt được", "thu được"],
      "cho": ["trao", "tặng", "cung cấp", "ban cho", "dành cho"],
      "lấy": ["thu lấy", "chiếm lấy", "nhận lấy", "giành lấy"],
      "đưa": ["mang đến", "chuyển đến", "giao", "trao"],
      "ra": ["xuất hiện", "đi ra", "thoát ra", "phát sinh"],
      "vào": ["tiến vào", "bước vào", "gia nhập", "tham gia"],
      "về": ["trở về", "quay về", "liên quan", "thuộc về"],
      "lên": ["tăng lên", "đi lên", "leo lên", "nâng lên"],
      "xuống": ["giảm xuống", "đi xuống", "hạ xuống", "rơi xuống"],

      // Common adjectives (tính từ)
      "tốt": ["xuất sắc", "tuyệt vời", "hoàn hảo", "ưu việt", "chất lượng"],
      "xấu": ["tệ", "dở", "kém", "không tốt", "tồi tệ"],
      "lớn": ["to", "khổng lồ", "rộng lớn", "đồ sộ", "vĩ đại"],
      "nhỏ": ["bé", "tí hon", "khiêm tốn", "hạn chế", "thu nhỏ"],
      "mới": ["hiện đại", "đương đại", "gần đây", "tươi mới", "tiên tiến"],
      "cũ": ["xưa", "cổ", "truyền thống", "lâu đời", "lỗi thời"],
      "cao": ["lên cao", "vượt trội", "ưu việt", "tối cao", "đỉnh cao"],
      "thấp": ["hạ", "giảm", "khiêm tốn", "thấp kém", "hạn chế"],
      "dài": ["kéo dài", "lâu dài", "kéo dài", "mở rộng"],
      "ngắn": ["tóm tắt", "gọn gàng", "hạn chế", "thu gọn"],
      "quan trọng": ["thiết yếu", "cần thiết", "then chốt", "chủ yếu", "trọng yếu"],
      "khác": ["khác biệt", "riêng biệt", "đặc biệt", "phân biệt"],
      "giống": ["tương tự", "như nhau", "đồng nhất", "tương đồng"],
      "đúng": ["chính xác", "đúng đắn", "hợp lý", "phù hợp"],
      "sai": ["không đúng", "lầm lẫn", "nhầm lẫn", "không chính xác"],

      // Common nouns (danh từ)
      "người": ["con người", "cá nhân", "nhân vật", "đối tượng"],
      "việc": ["công việc", "nhiệm vụ", "hoạt động", "sự việc"],
      "thời gian": ["thời điểm", "khoảng thời gian", "giai đoạn", "thời kỳ"],
      "cách": ["phương pháp", "biện pháp", "thủ thuật", "kỹ thuật"],
      "ngày": ["hôm", "thời điểm", "dịp", "buổi"],
      "năm": ["năm tháng", "thời gian", "giai đoạn", "thời kỳ"],
      "nước": ["quốc gia", "đất nước", "chất lỏng", "nước uống"],
      "nhà": ["gia đình", "tổ ấm", "căn hộ", "ngôi nhà"],
      "thế giới": ["toàn cầu", "địa cầu", "vũ trụ", "thế gian"],
      "cuộc sống": ["đời sống", "sinh hoạt", "tồn tại", "kiếp sống"],
      "tay": ["bàn tay", "cánh tay", "chi tay", "tứ chi"],
      "phần": ["bộ phận", "khu vực", "thành phần", "mảng"],
      "nơi": ["địa điểm", "vị trí", "chỗ", "khu vực"],
      "trường hợp": ["tình huống", "hoàn cảnh", "trường hợp", "trạng thái"],
      "điểm": ["khía cạnh", "yếu tố", "đặc điểm", "chi tiết"],

      // Transition words (từ nối)
      "tuy nhiên": ["nhưng", "song", "thế nhưng", "dù vậy", "mặc dù vậy"],
      "do đó": ["vì vậy", "cho nên", "bởi thế", "vì thế", "nên"],
      "bởi vì": ["vì", "do", "nhờ", "tại vì", "bởi"],
      "cũng": ["nữa", "thêm vào đó", "hơn nữa", "đồng thời"],
      "nhưng": ["tuy nhiên", "song", "thế nhưng", "dù vậy"],
      "và": ["cùng với", "kèm theo", "bên cạnh", "thêm vào"],
      "hoặc": ["hay", "hay là", "thay vào đó", "có thể"],
      "vậy": ["như vậy", "do đó", "bởi thế", "vì thế"],
      "sau đó": ["tiếp theo", "kế tiếp", "rồi", "về sau"],
      "bây giờ": ["hiện tại", "lúc này", "thời điểm này", "ngay lúc này"],

      // Academic terms (thuật ngữ học thuật)
      "nghiên cứu": ["tìm hiểu", "khảo sát", "điều tra", "phân tích"],
      "kết quả": ["hậu quả", "thành quả", "tác động", "hiệu quả"],
      "vấn đề": ["khó khăn", "thách thức", "trở ngại", "rắc rối"],
      "giải pháp": ["cách giải quyết", "phương án", "biện pháp", "đáp án"],
      "phương pháp": ["cách thức", "kỹ thuật", "thủ thuật", "biện pháp"],
      "dữ liệu": ["thông tin", "số liệu", "tài liệu", "bằng chứng"],
      "phân tích": ["đánh giá", "xem xét", "nghiên cứu", "khảo sát"],
      "phát triển": ["tạo ra", "xây dựng", "hình thành", "thiết lập"],

      // Business terms (thuật ngữ kinh doanh)
      "công ty": ["doanh nghiệp", "tổ chức", "cơ quan", "tập đoàn"],
      "dự án": ["kế hoạch", "chương trình", "nhiệm vụ", "công việc"],
      "nhóm": ["đội", "tập thể", "ban", "bộ phận"],
      "cuộc họp": ["hội nghị", "buổi thảo luận", "phiên họp", "tọa đàm"],
      "kế hoạch": ["chiến lược", "phương án", "đề án", "chương trình"],
      "mục tiêu": ["đích", "mục đích", "ý định", "định hướng"],
      "thành công": ["thắng lợi", "chiến thắng", "đạt được", "hoàn thành"],
      "thất bại": ["thất bại", "không thành công", "thất thế", "thua cuộc"],
      "cơ hội": ["dịp", "khả năng", "triển vọng", "tiềm năng"],
      "thách thức": ["khó khăn", "trở ngại", "rào cản", "vấn đề"],

      // Time expressions (biểu thức thời gian)
      "hôm nay": ["ngày hôm nay", "hôm nay", "bây giờ"],
      "hôm qua": ["ngày hôm qua", "trước đây"],
      "ngày mai": ["mai", "tương lai", "sắp tới"],
      "tuần này": ["tuần hiện tại", "tuần này"],
      "tháng này": ["tháng hiện tại", "tháng này"],
      "năm nay": ["năm hiện tại", "năm này"],

      // Emotions and feelings (cảm xúc)
      "vui": ["hạnh phúc", "phấn khích", "hài lòng", "vui mừng"],
      "buồn": ["u sầu", "thất vọng", "đau khổ", "lo lắng"],
      "giận": ["tức giận", "bực bội", "phẫn nộ", "khó chịu"],
      "sợ": ["lo sợ", "hoảng sợ", "e ngại", "lo lắng"],
      "yêu": ["thương", "quý", "trân trọng", "yêu mến"],

      // Colors (màu sắc)
      "đỏ": ["đỏ thắm", "đỏ rực", "màu đỏ", "son"],
      "xanh": ["xanh lam", "xanh dương", "xanh biển"],
      "vàng": ["vàng óng", "vàng rực", "màu vàng"],
      "trắng": ["trắng tinh", "trắng muốt", "màu trắng"],
      "đen": ["đen tuyền", "đen thui", "màu đen"],

      // Family terms (thuật ngữ gia đình)
      "cha": ["bố", "ba", "thầy", "ông"],
      "mẹ": ["má", "mẹ", "bà"],
      "con": ["đứa con", "con cái", "cháu"],
      "anh": ["anh trai", "người anh"],
      "chị": ["chị gái", "người chị"],
      "em": ["em trai", "em gái", "người em"]
    };
  }

  /**
   * Handles Vietnamese diacritics properly
   */
  private normalizeDiacritics(text: string): string {
    // Vietnamese diacritic normalization
    const diacriticMap: { [key: string]: string } = {
      'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
      'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
      'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
      'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
      'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
      'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
      'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
      'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
      'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
      'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
      'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
      'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
      'đ': 'd', 'Đ': 'D'
    };

    return text.replace(/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ]/g, 
      (match) => diacriticMap[match] || match);
  }

  private applyFormalStyle(text: string): string {
    // Apply formal Vietnamese style
    const formalReplacements = {
      "tôi": "tôi",
      "mình": "tôi",
      "tao": "tôi",
      "may": "bạn",
      "m": "bạn"
    };

    let result = text;
    for (const [informal, formal] of Object.entries(formalReplacements)) {
      const regex = this.createWordBoundaryRegex(informal);
      result = result.replace(regex, formal);
    }

    return result;
  }

  private applyCasualStyle(text: string): string {
    // Apply casual Vietnamese style
    return text;
  }

  private applyAcademicStyle(text: string): string {
    // Apply academic Vietnamese style
    const academicReplacements = {
      "cho thấy": "chứng minh",
      "dùng": "sử dụng",
      "giúp": "hỗ trợ",
      "bắt đầu": "khởi đầu",
      "kết thúc": "kết luận"
    };

    let result = text;
    for (const [simple, academic] of Object.entries(academicReplacements)) {
      const regex = this.createWordBoundaryRegex(simple);
      result = result.replace(regex, academic);
    }

    return result;
  }

  private applyCreativeStyle(text: string): string {
    // Apply creative Vietnamese style
    return text;
  }

  private applyTechnicalStyle(text: string): string {
    // Apply technical Vietnamese style
    return text;
  }

  private applyBalancedStyle(text: string): string {
    // Apply balanced Vietnamese style
    return text;
  }
}
