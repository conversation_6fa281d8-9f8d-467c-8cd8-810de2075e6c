import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import SecurityProvider from '@/components/SecurityProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'GhostLayer - AI Text Humanization Tool',
    template: '%s | GhostLayer'
  },
  description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent. Advanced AI humanization technology for researchers, writers, and content creators.',
  keywords: [
    'AI text humanization',
    'content transformation',
    'AI detection bypass',
    'text paraphrasing',
    'AI writing tool',
    'content optimization',
    'text enhancement',
    'academic writing',
    'content creation',
    'AI content humanizer'
  ],
  authors: [{ name: 'GhostLayer Team' }],
  creator: '<PERSON><PERSON>ay<PERSON>',
  publisher: 'GhostLayer',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ghostlayer.app',
    title: 'GhostLayer - AI Text Humanization Tool',
    description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent. Advanced AI humanization technology for researchers, writers, and content creators.',
    siteName: 'GhostLayer',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'GhostLayer - AI Text Humanization Tool',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GhostLayer - AI Text Humanization Tool',
    description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent.',
    images: ['/og-image.jpg'],
    creator: '@ghostlayer',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

const structuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "GhostLayer",
  "description": "Transform AI-generated content into natural, human-like writing while preserving meaning and intent.",
  "url": "https://ghostlayer.app",
  "applicationCategory": "BusinessApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  },
  "creator": {
    "@type": "Organization",
    "name": "GhostLayer Team"
  },
  "featureList": [
    "AI Text Humanization",
    "Content Transformation",
    "Multiple Writing Styles",
    "Real-time Processing",
    "Privacy-Focused"
  ],
  "screenshot": "https://ghostlayer.app/screenshot.jpg"
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />

        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/logo.jpg" type="image/jpeg" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#1e293b" />
        <meta name="msapplication-TileColor" content="#1e293b" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />

        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-XXXXXXXXXX');
            `,
          }}
        />
      </head>
      <body className={inter.className}>
        <SecurityProvider>
          {children}
        </SecurityProvider>
      </body>
    </html>
  );
}