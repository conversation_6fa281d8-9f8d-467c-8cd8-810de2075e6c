/**
 * Base Language Processor for GhostLayer
 * Abstract base class for all language-specific processors
 */

import { LanguageProcessor, SupportedLanguage, TextSegment, SynonymDictionary } from '../types';
import { getLanguageConfig, createWordBoundaryRegex, segmentSentences, segmentWords } from '../config';

export abstract class BaseProcessor implements LanguageProcessor {
  public readonly language: SupportedLanguage;
  protected config: ReturnType<typeof getLanguageConfig>;
  protected synonyms: SynonymDictionary = {};

  constructor(language: SupportedLanguage) {
    this.language = language;
    this.config = getLanguageConfig(language);
  }

  /**
   * Segments text into meaningful units (sentences, words, punctuation)
   */
  segmentText(text: string): TextSegment[] {
    const sentences = segmentSentences(text, this.language);
    const segments: TextSegment[] = [];
    let currentIndex = 0;

    for (const sentence of sentences) {
      const startIndex = text.indexOf(sentence, currentIndex);
      const endIndex = startIndex + sentence.length;

      segments.push({
        content: sentence,
        type: 'sentence',
        language: this.language,
        startIndex,
        endIndex
      });

      currentIndex = endIndex;
    }

    return segments;
  }

  /**
   * Creates a word boundary regex appropriate for this language
   */
  createWordBoundaryRegex(word: string): RegExp {
    return createWordBoundaryRegex(word, this.language);
  }

  /**
   * Applies synonym replacement with specified intensity
   */
  applySynonymReplacement(text: string, synonyms: Record<string, string[]>, intensity: number): string {
    if (!synonyms || Object.keys(synonyms).length === 0) {
      return text;
    }

    let result = text;
    const replacementProbability = this.getReplacementProbability(intensity);
    const processedWords = new Set<string>();

    // Sort words by length (longest first) to avoid partial replacements
    const sortedWords = Object.keys(synonyms).sort((a, b) => b.length - a.length);

    for (const word of sortedWords) {
      if (processedWords.has(word.toLowerCase())) continue;

      const synonymList = synonyms[word];
      if (!synonymList || synonymList.length === 0) continue;

      // Create language-appropriate regex
      const regex = this.createWordBoundaryRegex(word);
      
      result = result.replace(regex, (match) => {
        // Apply replacement based on intensity probability
        if (Math.random() < replacementProbability) {
          const randomSynonym = synonymList[Math.floor(Math.random() * synonymList.length)];
          
          // Preserve original case
          if (match === match.toUpperCase()) {
            return randomSynonym.toUpperCase();
          } else if (match[0] === match[0].toUpperCase()) {
            return randomSynonym.charAt(0).toUpperCase() + randomSynonym.slice(1).toLowerCase();
          } else {
            return randomSynonym.toLowerCase();
          }
        }
        return match;
      });

      processedWords.add(word.toLowerCase());
    }

    return result;
  }

  /**
   * Optimizes text for specific writing style
   */
  abstract optimizeForStyle(text: string, style: string): string;

  /**
   * Validates text for processing
   */
  validateText(text: string): { isValid: boolean; error?: string } {
    if (!text || typeof text !== 'string') {
      return { isValid: false, error: 'Text must be a non-empty string' };
    }

    if (text.trim().length === 0) {
      return { isValid: false, error: 'Text cannot be empty' };
    }

    if (text.length > 50000) {
      return { isValid: false, error: 'Text exceeds maximum length of 50,000 characters' };
    }

    return { isValid: true };
  }

  /**
   * Gets replacement probability based on intensity
   */
  protected getReplacementProbability(intensity: number): number {
    // intensity is between 0 and 1
    return Math.min(0.8, Math.max(0.1, intensity));
  }

  /**
   * Preserves text formatting (paragraphs, line breaks, etc.)
   */
  protected preserveFormatting(originalText: string, processedText: string): string {
    // Simple formatting preservation - can be enhanced
    const originalLines = originalText.split('\n');
    const processedLines = processedText.split('\n');

    if (originalLines.length === processedLines.length) {
      return processedText;
    }

    // If line counts don't match, try to preserve paragraph structure
    const paragraphs = originalText.split(/\n\s*\n/);
    if (paragraphs.length > 1) {
      // Process each paragraph separately and rejoin
      const processedParagraphs = paragraphs.map(paragraph => {
        if (paragraph.trim().length === 0) return paragraph;
        // This is a simplified approach - in practice, you'd need more sophisticated logic
        return processedText;
      });
      return processedParagraphs.join('\n\n');
    }

    return processedText;
  }

  /**
   * Calculates text complexity score
   */
  protected calculateComplexity(text: string): number {
    const words = segmentWords(text, this.language);
    const sentences = segmentSentences(text, this.language);
    
    if (sentences.length === 0) return 0;

    const avgWordsPerSentence = words.length / sentences.length;
    const avgCharsPerWord = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    
    // Simple complexity score (can be enhanced with more sophisticated metrics)
    return (avgWordsPerSentence * 0.5) + (avgCharsPerWord * 0.3);
  }

  /**
   * Loads synonym dictionary for this language
   */
  protected abstract loadSynonyms(): Promise<SynonymDictionary>;

  /**
   * Initializes the processor (loads synonyms, etc.)
   */
  async initialize(): Promise<void> {
    try {
      this.synonyms = await this.loadSynonyms();
    } catch (error) {
      console.warn(`Failed to load synonyms for ${this.language}:`, error);
      this.synonyms = {};
    }
  }

  /**
   * Gets processing statistics
   */
  getProcessingStats(originalText: string, processedText: string) {
    const originalWords = segmentWords(originalText, this.language);
    const processedWords = segmentWords(processedText, this.language);
    const originalSentences = segmentSentences(originalText, this.language);
    const processedSentences = segmentSentences(processedText, this.language);

    return {
      originalWordCount: originalWords.length,
      processedWordCount: processedWords.length,
      originalSentenceCount: originalSentences.length,
      processedSentenceCount: processedSentences.length,
      originalCharCount: originalText.length,
      processedCharCount: processedText.length,
      complexityScore: this.calculateComplexity(processedText),
      changePercentage: Math.abs(processedText.length - originalText.length) / originalText.length * 100
    };
  }
}
