/**
 * Multi-Language Text Processor for GhostLayer
 * Coordinates language detection and processing across all supported languages
 */

import { 
  SupportedLanguage, 
  LanguageProcessor, 
  ProcessingOptions, 
  MultiLanguageProcessingResult 
} from './types';
import { detectLanguage, validateLanguageSupport } from './detector';
import { getLanguageConfig } from './config';

export class MultiLanguageProcessor {
  private processors: Map<SupportedLanguage, LanguageProcessor> = new Map();
  private initialized = false;

  constructor() {
    // Processors will be loaded dynamically to avoid bundle bloat
  }

  /**
   * Initialize the multi-language processor
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Initialize with English processor by default
      await this.loadProcessor('en');
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize MultiLanguageProcessor:', error);
      throw error;
    }
  }

  /**
   * Process text with automatic language detection
   */
  async processText(
    text: string,
    options: Partial<ProcessingOptions> = {}
  ): Promise<MultiLanguageProcessingResult> {
    const startTime = Date.now();

    try {
      // Validate input
      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        throw new Error('Invalid input: text must be a non-empty string');
      }

      // Detect language if not specified
      const detection = detectLanguage(text);
      const detectedLanguage = options.targetLanguage || detection.language;

      // Validate language support
      const validation = validateLanguageSupport(text, detectedLanguage);
      if (!validation.isSupported) {
        throw new Error(validation.message || 'Language not supported');
      }

      // Load appropriate processor
      await this.loadProcessor(detectedLanguage);
      const processor = this.processors.get(detectedLanguage);
      
      if (!processor) {
        throw new Error(`Processor not available for language: ${detectedLanguage}`);
      }

      // Prepare complete options with defaults
      const completeOptions: ProcessingOptions = {
        intensity: options.intensity || 'medium',
        style: options.style || 'balanced',
        preserveFormat: options.preserveFormat !== undefined ? options.preserveFormat : true,
        addVariations: options.addVariations || false,
        targetLanguage: detectedLanguage,
        autoDetectLanguage: options.autoDetectLanguage !== undefined ? options.autoDetectLanguage : true
      };

      // Process the text
      const humanizedText = await this.processWithProcessor(processor, text, completeOptions);

      // Calculate metrics
      const processingTime = Date.now() - startTime;
      const stats = processor.getProcessingStats(text, humanizedText);

      // Generate variations if requested
      let variations: string[] | undefined;
      if (completeOptions.addVariations) {
        variations = await this.generateVariations(processor, text, completeOptions, 3);
      }

      return {
        originalText: text,
        humanizedText,
        detectedLanguage,
        confidence: detection.confidence,
        variations,
        improvementScore: this.calculateImprovementScore(text, humanizedText),
        detectionScore: detection.confidence,
        readabilityScore: this.calculateReadabilityScore(humanizedText, detectedLanguage),
        processingTime,
        originalLength: text.length,
        newLength: humanizedText.length,
        languageFeatures: {
          wordCount: stats.processedWordCount,
          sentenceCount: stats.processedSentenceCount,
          characterCount: stats.processedCharCount,
          complexityScore: stats.complexityScore
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      // Return error result
      return {
        originalText: text,
        humanizedText: text, // Return original text on error
        detectedLanguage: 'en',
        confidence: 0,
        improvementScore: 0,
        detectionScore: 0,
        readabilityScore: 0,
        processingTime,
        originalLength: text.length,
        newLength: text.length,
        languageFeatures: {
          wordCount: 0,
          sentenceCount: 0,
          characterCount: text.length,
          complexityScore: 0
        }
      };
    }
  }

  /**
   * Load processor for specific language dynamically
   */
  private async loadProcessor(language: SupportedLanguage): Promise<void> {
    if (this.processors.has(language)) {
      return; // Already loaded
    }

    try {
      let ProcessorClass: any;

      // Dynamic import to avoid bundle bloat
      switch (language) {
        case 'en':
          const { EnglishProcessor } = await import('./processors/EnglishProcessor');
          ProcessorClass = EnglishProcessor;
          break;
        
        case 'vi':
          const { VietnameseProcessor } = await import('./processors/VietnameseProcessor');
          ProcessorClass = VietnameseProcessor;
          break;
        
        case 'zh':
        case 'zh-tw':
          const { ChineseProcessor } = await import('./processors/ChineseProcessor');
          ProcessorClass = ChineseProcessor;
          break;
        
        default:
          throw new Error(`Unsupported language: ${language}`);
      }

      const processor = new ProcessorClass();
      await processor.initialize();
      this.processors.set(language, processor);

    } catch (error) {
      console.error(`Failed to load processor for ${language}:`, error);
      
      // Fallback to English processor
      if (language !== 'en') {
        console.warn(`Falling back to English processor for ${language}`);
        await this.loadProcessor('en');
        const englishProcessor = this.processors.get('en');
        if (englishProcessor) {
          this.processors.set(language, englishProcessor);
        }
      } else {
        throw error;
      }
    }
  }

  /**
   * Process text with specific processor
   */
  private async processWithProcessor(
    processor: LanguageProcessor,
    text: string,
    options: ProcessingOptions
  ): Promise<string> {
    // Validate text
    const validation = processor.validateText(text);
    if (!validation.isValid) {
      throw new Error(validation.error || 'Text validation failed');
    }

    let result = text;

    // Apply synonym replacement
    if (processor.synonyms && Object.keys(processor.synonyms).length > 0) {
      const intensity = this.getIntensityValue(options.intensity || 'medium');
      result = processor.applySynonymReplacement(result, processor.synonyms, intensity);
    }

    // Apply style optimization
    if (options.style) {
      result = processor.optimizeForStyle(result, options.style);
    }

    return result;
  }

  /**
   * Generate text variations
   */
  private async generateVariations(
    processor: LanguageProcessor,
    text: string,
    options: ProcessingOptions,
    count: number
  ): Promise<string[]> {
    const variations: string[] = [];
    
    for (let i = 0; i < count; i++) {
      try {
        // Vary the intensity for different variations
        const intensities = ['light', 'medium', 'heavy'] as const;
        const intensity = intensities[i % intensities.length];
        
        const variationOptions = { ...options, intensity };
        const variation = await this.processWithProcessor(processor, text, variationOptions);
        
        if (variation !== text && !variations.includes(variation)) {
          variations.push(variation);
        }
      } catch (error) {
        console.warn(`Failed to generate variation ${i + 1}:`, error);
      }
    }

    return variations;
  }

  /**
   * Calculate improvement score
   */
  private calculateImprovementScore(original: string, processed: string): number {
    if (original === processed) return 0;
    
    // Simple improvement score based on text changes
    const changeRatio = Math.abs(processed.length - original.length) / original.length;
    const wordChanges = this.countWordChanges(original, processed);
    
    // Score between 0-100
    return Math.min(100, Math.max(0, (changeRatio * 30) + (wordChanges * 10)));
  }

  /**
   * Calculate readability score
   */
  private calculateReadabilityScore(text: string, language: SupportedLanguage): number {
    const config = getLanguageConfig(language);
    
    // Simple readability calculation
    const sentences = text.split(config.sentenceEndPattern).filter(s => s.trim().length > 0);
    const words = text.match(/\S+/g) || [];
    
    if (sentences.length === 0) return 0;
    
    const avgWordsPerSentence = words.length / sentences.length;
    const avgCharsPerWord = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    
    // Readability score (simplified)
    let score = 100;
    
    // Penalize very long sentences
    if (avgWordsPerSentence > 20) {
      score -= (avgWordsPerSentence - 20) * 2;
    }
    
    // Penalize very long words
    if (avgCharsPerWord > 6) {
      score -= (avgCharsPerWord - 6) * 3;
    }
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Count word changes between original and processed text
   */
  private countWordChanges(original: string, processed: string): number {
    const originalWords = original.toLowerCase().match(/\S+/g) || [];
    const processedWords = processed.toLowerCase().match(/\S+/g) || [];
    
    let changes = 0;
    const maxLength = Math.max(originalWords.length, processedWords.length);
    
    for (let i = 0; i < maxLength; i++) {
      if (originalWords[i] !== processedWords[i]) {
        changes++;
      }
    }
    
    return changes;
  }

  /**
   * Convert intensity string to numeric value
   */
  private getIntensityValue(intensity: 'light' | 'medium' | 'heavy'): number {
    switch (intensity) {
      case 'light': return 0.3;
      case 'medium': return 0.5;
      case 'heavy': return 0.7;
      default: return 0.5;
    }
  }

  /**
   * Get list of supported languages
   */
  getSupportedLanguages(): SupportedLanguage[] {
    return ['en', 'vi', 'zh', 'zh-tw'];
  }

  /**
   * Check if a language is supported
   */
  isLanguageSupported(language: string): language is SupportedLanguage {
    return this.getSupportedLanguages().includes(language as SupportedLanguage);
  }

  /**
   * Get processor for a specific language (loads if needed)
   */
  async getProcessor(language: SupportedLanguage): Promise<LanguageProcessor | null> {
    try {
      await this.loadProcessor(language);
      return this.processors.get(language) || null;
    } catch (error) {
      console.error(`Failed to get processor for ${language}:`, error);
      return null;
    }
  }
}
