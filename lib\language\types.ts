/**
 * Language support types and interfaces for GhostLayer
 */

export type SupportedLanguage = 'en' | 'vi' | 'zh' | 'zh-tw';

export interface LanguageConfig {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  wordBoundaryPattern: RegExp;
  sentenceEndPattern: RegExp;
  punctuation: string[];
  hasSpaces: boolean;
  direction: 'ltr' | 'rtl';
  unicodeRanges: RegExp[];
}

export interface LanguageDetectionResult {
  language: SupportedLanguage;
  confidence: number;
  detectedFeatures: {
    hasChineseChars: boolean;
    hasVietnameseDiacritics: boolean;
    hasLatinChars: boolean;
    punctuationStyle: 'western' | 'chinese' | 'mixed';
  };
}

export interface TextSegment {
  content: string;
  type: 'sentence' | 'word' | 'punctuation';
  language: SupportedLanguage;
  startIndex: number;
  endIndex: number;
}

export interface LanguageProcessor {
  language: SupportedLanguage;
  synonyms?: SynonymDictionary;
  segmentText(text: string): TextSegment[];
  createWordBoundaryRegex(word: string): RegExp;
  applySynonymReplacement(text: string, synonyms: Record<string, string[]>, intensity: number): string;
  optimizeForStyle(text: string, style: string): string;
  validateText(text: string): { isValid: boolean; error?: string };
  getProcessingStats(originalText: string, processedText: string): {
    originalWordCount: number;
    processedWordCount: number;
    originalSentenceCount: number;
    processedSentenceCount: number;
    originalCharCount: number;
    processedCharCount: number;
    complexityScore: number;
    changePercentage: number;
  };
}

export interface SynonymDictionary {
  [word: string]: string[];
}

export interface LanguageSynonyms {
  en: SynonymDictionary;
  vi: SynonymDictionary;
  zh: SynonymDictionary;
  'zh-tw': SynonymDictionary;
}

export interface ProcessingOptions {
  intensity: 'light' | 'medium' | 'heavy';
  style: 'formal' | 'casual' | 'academic' | 'creative' | 'technical' | 'balanced';
  preserveFormat: boolean;
  addVariations: boolean;
  targetLanguage?: SupportedLanguage;
  autoDetectLanguage?: boolean;
}

export interface MultiLanguageProcessingResult {
  originalText: string;
  humanizedText: string;
  detectedLanguage: SupportedLanguage;
  confidence: number;
  variations?: string[];
  improvementScore: number;
  detectionScore: number;
  readabilityScore: number;
  processingTime: number;
  originalLength: number;
  newLength: number;
  languageFeatures: {
    wordCount: number;
    sentenceCount: number;
    characterCount: number;
    complexityScore: number;
  };
}
