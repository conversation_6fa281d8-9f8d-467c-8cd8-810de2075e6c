# GhostLayer: AI Text Humanization Tool

Transform AI-generated content into natural, human-like writing while preserving meaning and intent.

![GhostLay<PERSON> Banner](https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg?auto=compress&cs=tinysrgb&w=1200&h=400&fit=crop)

## 🚀 Features

- **Advanced Text Processing**: Multi-stage humanization using paraphrasing, style optimization, and variation generation
- **AI Detection Analysis**: Real-time scoring to measure how human-like your content appears
- **Multiple Writing Styles**: Support for formal, casual, academic, creative, and technical writing styles
- **Customizable Intensity**: Light, medium, or heavy transformation options
- **Side-by-Side Comparison**: Compare original AI text with humanized versions
- **Multiple Variations**: Generate different versions of the same content
- **Export Functionality**: Download processed text in various formats
- **Drag & Drop Support**: Easy file upload for batch processing
- **Real-time Metrics**: Word count, character count, and processing time tracking
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

## 🛠️ Tech Stack

- **Frontend**: Next.js 13+ with App Router
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI primitives with shadcn/ui
- **Icons**: Lucide React
- **Language**: TypeScript
- **Analytics**: Google Analytics (Privacy-focused)
- **Deployment**: Netlify (Static Export)

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- Node.js 18.0 or higher
- npm or yarn package manager
- Git

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/HectorTa1989/ghostlayer.git
   cd ghostlayer
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000` to see the application running.

## 🚀 Deployment on Netlify

### Method 1: Deploy from Git Repository

1. **Push your code to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Connect to Netlify**
   - Go to [Netlify](https://netlify.com) and sign in
   - Click "New site from Git"
   - Choose your Git provider (GitHub, GitLab, or Bitbucket)
   - Select your repository

3. **Configure build settings**
   - Build command: `npm run build`
   - Publish directory: `out`
   - Node version: `18`

4. **Deploy**
   - Click "Deploy site"
   - Your site will be available at a generated URL (e.g., `https://amazing-site-name.netlify.app`)

### Method 2: Manual Deploy

1. **Build the project**
   ```bash
   npm run build
   ```

2. **Deploy to Netlify**
   - Go to [Netlify](https://netlify.com)
   - Drag and drop the `out` folder to the deploy area
   - Your site will be live instantly

### Method 3: Netlify CLI

1. **Install Netlify CLI**
   ```bash
   npm install -g netlify-cli
   ```

2. **Login to Netlify**
   ```bash
   netlify login
   ```

3. **Build and deploy**
   ```bash
   npm run build
   netlify deploy --prod --dir=out
   ```

## Set Up Google Analytics 4
Create GA4 Property:
- Go to analytics.google.com
- Sign in with your Google account
- Click "Start measuring" or "Create Account"
- Set up your property:
   - Account name: "GhostLayer"
   - Property name: "GhostLayer App"
   - Time zone: Your timezone
   - Currency: Your currency
- Choose "Web" as your platform
- Enter your website URL and stream name
- Copy your Measurement ID (format: G-XXXXXXXXXX)

## Access Your Google Analytics Dashboard
Once deployed, you can view your traffic at:

- Google Analytics: analytics.google.com
- Navigate to GhostLayer property   

## 🔧 Configuration

The application is configured for static export and Netlify deployment through:

- `next.config.js`: Enables static export and optimizes for deployment
- `netlify.toml`: Configures build settings and redirects
- `package.json`: Includes build and export scripts

## 📁 Project Structure

```
GhostLayer/
├── app/                          # Next.js App Router
│   ├── api/                      # API routes
│   │   ├── detect/              # AI detection endpoint
│   │   └── process/             # Text processing endpoint
│   ├── globals.css              # Global styles
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Main page component
├── components/                   # React components
│   ├── ui/                      # shadcn/ui components
│   ├── DetectionScore.tsx       # AI detection analysis
│   ├── Header.tsx               # App header
│   ├── OutputDisplay.tsx        # Results display
│   ├── ProcessingOptions.tsx    # Settings panel
│   └── TextInput.tsx            # Input interface
├── lib/                         # Utility libraries
│   ├── aiDetection/            # AI detection logic
│   ├── textProcessor/          # Text processing engines
│   └── utils.ts                # Utility functions
├── types/                       # TypeScript definitions
├── public/                      # Static assets
├── netlify.toml                # Netlify configuration
├── next.config.js              # Next.js configuration
└── package.json                # Dependencies and scripts
```

## 🎯 Usage

1. **Input Text**: Paste or type your AI-generated content into the input area
2. **Configure Options**: Adjust transformation intensity and writing style
3. **Process**: Click "Humanize Text" to transform your content
4. **Review Results**: Compare original and humanized versions
5. **Export**: Download your processed text or copy to clipboard

## 🔍 Text Processing Features

### Transformation Intensity
- **Light**: Minimal changes, preserves original structure
- **Medium**: Balanced transformation with good readability
- **Heavy**: Maximum humanization with significant restructuring

### Writing Styles
- **Balanced**: Natural, versatile writing suitable for most content
- **Formal**: Professional tone for business and academic content
- **Casual**: Conversational style for blogs and social media
- **Academic**: Scholarly tone with complex sentence structures
- **Creative**: Expressive language for storytelling and marketing
- **Technical**: Precise terminology for documentation and guides

### Processing Algorithms
- **Paraphrasing Engine**: Advanced synonym replacement and sentence restructuring
- **Style Optimizer**: Tone and voice adjustment based on selected style
- **Variation Generator**: Creates multiple versions for comparison
- **AI Detection Scorer**: Analyzes text for AI-generated patterns

## 🔧 Customization

### Styling
The application uses Tailwind CSS with a custom design system. Key colors:
- Primary: Blue gradient (`from-blue-600 to-purple-600`)
- Background: Dark slate (`slate-900`)
- Cards: Glass effect (`bg-white/5 backdrop-blur-lg`)

### Adding New Processing Algorithms
1. Create a new file in `lib/textProcessor/`
2. Export your processing function
3. Import and integrate in the main processor
4. Update the options interface if needed

### Extending AI Detection
1. Add new detection methods in `lib/aiDetection/detector.ts`
2. Update the scoring algorithm
3. Modify the UI components to display new metrics

## 🐛 Troubleshooting

### Build Issues
- Ensure Node.js version is 18 or higher
- Clear node_modules and reinstall dependencies
- Check for TypeScript errors: `npm run lint`

### Deployment Issues
- Verify `out` directory is generated after build
- Check Netlify build logs for specific errors
- Ensure all dependencies are in `package.json`

### Performance Issues
- Large text inputs may take longer to process
- Consider implementing text chunking for very long content
- Monitor memory usage during processing

## 📊 Analytics & Privacy

GhostLayer uses **Plausible Analytics** for privacy-focused usage tracking. We collect minimal, anonymized data to improve the application.

### What We Track
- **Text Processing Events**: Style, intensity, text length, processing time
- **User Interactions**: Copy/download actions, option changes, tab switches
- **File Uploads**: File type and size (no content)
- **Errors**: Error types for debugging (no personal data)

### What We DON'T Track
- ❌ Personal information or user identity
- ❌ Text content or processed results
- ❌ IP addresses or location data
- ❌ Cookies or persistent identifiers

### Analytics Implementation

The analytics system is implemented using Plausible's privacy-focused approach:

```typescript
// Example: Track text processing
analytics.trackTextProcessed({
  style: 'academic',
  intensity: 'medium',
  textLength: 1500,
  processingTime: 250,
  hasVariations: true
});

// Example: Track user interactions
analytics.trackCopy('main');
analytics.trackDownload('variation', 2);
```

### Key Features
- **Privacy-First**: No personal data collection
- **GDPR Compliant**: Respects user privacy rights
- **Lightweight**: Minimal performance impact
- **Open Source**: Transparent implementation

### Opting Out
Analytics help us improve GhostLayer, but you can:
- Use browser ad blockers to block analytics scripts
- Disable JavaScript (though this will affect functionality)
- Contact us for enterprise deployments without analytics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) for the amazing React framework
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Radix UI](https://radix-ui.com/) for accessible component primitives
- [Lucide](https://lucide.dev/) for beautiful icons
- [Pexels](https://pexels.com/) for high-quality stock photos

## 📞 Support

If you encounter any issues or have questions:
- Open an issue on GitHub
- Check the troubleshooting section above
- Review the documentation

---

**Made by Hector Ta**