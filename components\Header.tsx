import { <PERSON>, Shield, Zap } from 'lucide-react';
import Image from 'next/image';

export default function Header() {
  return (
    <header className="text-center mb-12">
      <div className="flex items-center justify-center mb-6">
        <div className="relative">
          <div className="absolute inset-0 bg-blue-500/20 blur-xl rounded-full"></div>
          <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-2xl">
            <Image
              src="/logo.jpg"
              alt="GhostLayer Logo"
              width={48}
              height={48}
              className="w-12 h-12 rounded-lg object-cover"
            />
          </div>
        </div>
      </div>
      
      <h1 className="text-5xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
        GhostLayer
      </h1>
      
      <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
        Transform AI-generated content into natural, human-like writing while preserving meaning and intent.
      </p>
      
      <div className="flex flex-wrap justify-center gap-6 text-gray-400">
        <div className="flex items-center gap-2">
          <Shield className="w-5 h-5 text-green-400" />
          <span>Undetectable Content</span>
        </div>
        <div className="flex items-center gap-2">
          <Zap className="w-5 h-5 text-yellow-400" />
          <span>Lightning Fast</span>
        </div>
        <div className="flex items-center gap-2">
          <Brain className="w-5 h-5 text-blue-400" />
          <span>AI-Powered</span>
        </div>
      </div>
    </header>
  );
}