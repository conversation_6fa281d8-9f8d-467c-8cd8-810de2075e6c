/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true,
    // Security: Restrict image domains
    domains: [],
    // Prevent image optimization attacks
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // Security: Optimize build performance
  swcMinify: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
    // Remove React DevTools in production
    reactRemoveProperties: process.env.NODE_ENV === 'production',
  },

  // Security: Exclude specific paths from being processed
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // Security: Disable powered by header
  poweredByHeader: false,

  // Security: Compress responses
  compress: true,

  // Security: Environment variables validation
  env: {
    CUSTOM_KEY: process.env.NODE_ENV,
  },

  // Security: Webpack configuration
  webpack: (config, { dev, isServer }) => {
    // Security: Disable source maps in production
    if (!dev && !isServer) {
      config.devtool = false;
    }

    // Security: Add security-related optimizations
    config.optimization = {
      ...config.optimization,
      minimize: !dev,
    };

    return config;
  },
};

module.exports = nextConfig;
