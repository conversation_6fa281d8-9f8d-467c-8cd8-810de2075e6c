/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true,
    // Security: Restrict image domains
    domains: [],
    // Prevent image optimization attacks
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // Security: Optimize build performance
  swcMinify: true,
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
    // Remove React DevTools in production
    reactRemoveProperties: process.env.NODE_ENV === 'production',
  },

  // Security: Exclude specific paths from being processed
  pageExtensions: ['tsx', 'ts', 'jsx', 'js'],

  // Security: Disable powered by header
  poweredByHeader: false,

  // Security: Compress responses
  compress: true,

  // Security: Environment variables validation
  env: {
    CUSTOM_KEY: process.env.NODE_ENV,
  },

  // Security: Webpack configuration (simplified for build stability)
  webpack: (config, { dev, isServer }) => {
    // Only disable source maps in production for security
    if (!dev) {
      config.devtool = false;
    }

    // Exclude nodejieba from client-side bundle
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        'nodejieba': false,
        'mock-aws-s3': false,
        'aws-sdk': false,
        'nock': false,
        'fs': false,
        'path': false,
        'os': false
      };
    }

    return config;
  },
};

module.exports = nextConfig;
