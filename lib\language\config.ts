/**
 * Language Configuration System for GhostLayer
 * Defines language-specific processing rules and patterns
 */

import { LanguageConfig, SupportedLanguage } from './types';

/**
 * Language configurations for supported languages
 */
export const LANGUAGE_CONFIGS: Record<SupportedLanguage, LanguageConfig> = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    wordBoundaryPattern: /\b/,
    sentenceEndPattern: /(?<=[.!?])\s+/,
    punctuation: ['.', '!', '?', ';', ':', ',', '"', "'", '(', ')', '[', ']', '{', '}'],
    hasSpaces: true,
    direction: 'ltr',
    unicodeRanges: [
      /[a-zA-Z]/,
      /[\u00C0-\u017F]/, // Latin Extended-A
      /[\u0180-\u024F]/, // Latin Extended-B
    ]
  },

  vi: {
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng V<PERSON>',
    wordBoundaryPattern: /(?<![\p{L}\p{M}])(?=[\p{L}\p{M}])|(?<=[\p{L}\p{M}])(?![\p{L}\p{M}])/u,
    sentenceEndPattern: /(?<=[.!?])\s+/,
    punctuation: ['.', '!', '?', ';', ':', ',', '"', "'", '(', ')', '[', ']', '{', '}', '–', '—'],
    hasSpaces: true,
    direction: 'ltr',
    unicodeRanges: [
      /[a-zA-Z]/,
      /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ]/,
      /[\u00C0-\u017F]/, // Latin Extended-A
      /[\u1EA0-\u1EF9]/, // Vietnamese diacritics
    ]
  },

  zh: {
    code: 'zh',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    wordBoundaryPattern: /(?<=[\u4e00-\u9fff])|(?=[\u4e00-\u9fff])/,
    sentenceEndPattern: /[。！？]/,
    punctuation: ['。', '！', '？', '；', '：', '，', '、', '\u201c', '\u201d', '\u2018', '\u2019', '（', '）', '【', '】', '《', '》'],
    hasSpaces: false,
    direction: 'ltr',
    unicodeRanges: [
      /[\u4e00-\u9fff]/, // CJK Unified Ideographs
      /[\u3400-\u4dbf]/, // CJK Extension A
      /[\uf900-\ufaff]/, // CJK Compatibility Ideographs
      /[\u3000-\u303f]/, // CJK Symbols and Punctuation
    ]
  },

  'zh-tw': {
    code: 'zh-tw',
    name: 'Chinese (Traditional)',
    nativeName: '繁體中文',
    wordBoundaryPattern: /(?<=[\u4e00-\u9fff])|(?=[\u4e00-\u9fff])/,
    sentenceEndPattern: /[。！？]/,
    punctuation: ['。', '！', '？', '；', '：', '，', '、', '\u201c', '\u201d', '\u2018', '\u2019', '（', '）', '【', '】', '《', '》'],
    hasSpaces: false,
    direction: 'ltr',
    unicodeRanges: [
      /[\u4e00-\u9fff]/, // CJK Unified Ideographs
      /[\u3400-\u4dbf]/, // CJK Extension A
      /[\uf900-\ufaff]/, // CJK Compatibility Ideographs
      /[\u3000-\u303f]/, // CJK Symbols and Punctuation
    ]
  }
};

/**
 * Creates a Unicode-aware word boundary regex for the specified language
 */
export function createWordBoundaryRegex(word: string, language: SupportedLanguage): RegExp {
  const config = LANGUAGE_CONFIGS[language];
  
  // Escape special regex characters in the word
  const escapedWord = word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  
  switch (language) {
    case 'zh':
    case 'zh-tw':
      // Chinese: no traditional word boundaries, match exact characters
      // Use lookahead/lookbehind to avoid matching partial characters
      return new RegExp(`(?<![\u4e00-\u9fff])${escapedWord}(?![\u4e00-\u9fff])`, 'giu');
    
    case 'vi':
      // Vietnamese: Unicode word boundaries with diacritic support
      return new RegExp(`(?<!\\p{L})${escapedWord}(?!\\p{L})`, 'giu');
    
    case 'en':
    default:
      // English: traditional word boundaries
      return new RegExp(`\\b${escapedWord}\\b`, 'gi');
  }
}

/**
 * Segments text into sentences based on language-specific rules
 */
export function segmentSentences(text: string, language: SupportedLanguage): string[] {
  const config = LANGUAGE_CONFIGS[language];
  
  if (!text || text.trim().length === 0) {
    return [];
  }
  
  let sentences: string[];
  
  switch (language) {
    case 'zh':
    case 'zh-tw':
      // Chinese: split by Chinese punctuation marks
      sentences = text.split(/[。！？；]/).filter(s => s.trim().length > 0);
      break;
    
    case 'vi':
      // Vietnamese: similar to English but handle special cases
      sentences = text.split(config.sentenceEndPattern).filter(s => s.trim().length > 0);
      break;
    
    case 'en':
    default:
      // English: split by sentence-ending punctuation followed by space
      sentences = text.split(config.sentenceEndPattern).filter(s => s.trim().length > 0);
      break;
  }
  
  return sentences.map(s => s.trim());
}

/**
 * Segments text into words based on language-specific rules
 */
export function segmentWords(text: string, language: SupportedLanguage): string[] {
  const config = LANGUAGE_CONFIGS[language];
  
  if (!text || text.trim().length === 0) {
    return [];
  }
  
  let words: string[];
  
  switch (language) {
    case 'zh':
    case 'zh-tw':
      // Chinese: each character is potentially a word, but we'll use simple segmentation
      // For production, this should use a proper Chinese word segmentation library
      words = text.match(/[\u4e00-\u9fff]+|[a-zA-Z]+|\d+/g) || [];
      break;
    
    case 'vi':
      // Vietnamese: split by spaces but handle diacritics properly
      words = text.match(/[\p{L}\p{M}]+/gu) || [];
      break;
    
    case 'en':
    default:
      // English: split by word boundaries
      words = text.match(/\b\w+\b/g) || [];
      break;
  }
  
  return words.filter(word => word.trim().length > 0);
}

/**
 * Validates text format for the specified language
 */
export function validateTextFormat(text: string, language: SupportedLanguage): { isValid: boolean; issues: string[] } {
  const config = LANGUAGE_CONFIGS[language];
  const issues: string[] = [];
  
  if (!text || text.trim().length === 0) {
    issues.push('Text is empty');
    return { isValid: false, issues };
  }
  
  // Check for appropriate character sets
  const hasExpectedChars = config.unicodeRanges.some(range => range.test(text));
  if (!hasExpectedChars) {
    issues.push(`Text does not contain expected characters for ${config.name}`);
  }
  
  // Check text length constraints
  if (text.length < 10) {
    issues.push('Text is too short for reliable processing');
  }
  
  if (text.length > 50000) {
    issues.push('Text exceeds maximum length limit');
  }
  
  // Language-specific validations
  switch (language) {
    case 'zh':
    case 'zh-tw':
      // Check for minimum Chinese character density
      const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
      const chineseDensity = chineseChars / text.length;
      if (chineseDensity < 0.1) {
        issues.push('Text has insufficient Chinese character density');
      }
      break;
    
    case 'vi':
      // Check for Vietnamese diacritics presence
      const vietnameseChars = (text.match(/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ]/g) || []).length;
      if (vietnameseChars === 0 && text.length > 100) {
        issues.push('Text may not be Vietnamese (no diacritics found)');
      }
      break;
    
    case 'en':
      // Check for basic English patterns
      const englishWords = (text.match(/\b[a-zA-Z]+\b/g) || []).length;
      if (englishWords < 3 && text.length > 50) {
        issues.push('Text may not be English (insufficient English words)');
      }
      break;
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
}

/**
 * Gets the appropriate configuration for a language
 */
export function getLanguageConfig(language: SupportedLanguage): LanguageConfig {
  return LANGUAGE_CONFIGS[language];
}

/**
 * Checks if a language is supported
 */
export function isLanguageSupported(language: string): language is SupportedLanguage {
  return language in LANGUAGE_CONFIGS;
}
